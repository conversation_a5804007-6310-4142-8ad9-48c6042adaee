/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.CORE_TRANSFERS_TDS_ENABLED;
import static com.fitb.digital.bff.movemoney.service.activity.ActivityServiceTestUtil.*;
import static com.fitb.digital.bff.movemoney.util.CesBffActivityTypes.*;
import static com.fitb.digital.bff.movemoney.utils.ActivityUtils.mockTransferLimitsResponseClient;
import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.requests.ClientActivityRequest;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivity;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountService;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.WordUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ActivityServiceV1Test {
  @InjectMocks private ActivityServiceV1 activityServiceV1;

  @Mock private CesClient activityClient;

  @Mock private AccountService accountService;
  @Mock private ActivityServiceAsync activityServiceAsync;
  @Mock private AccountServiceAsync accountServiceAsync;
  @Mock private FeatureFlagService featureFlagService;
  @Mock private CoreTransfersActivityService coreTransfersActivityService;

  @Test
  void postActivityReturnsErrorIfPostTransferAndPayActivityFails() {
    when(activityClient.postTransferAndPayActivity(any(ClientActivityRequest.class)))
        .thenReturn(new ClientActivityResponse("ERROR", "BADBOY", "NO"));
    BffActivityRequest addActivity = new BffActivityRequest();
    addActivity.setToAccountId("toAccount-id");
    addActivity.setFromAccountId("fromAccount-id");
    var response = activityServiceV1.addActivity(addActivity);
    assertNotEquals(CesResponse.SUCCESS, response.getStatus());
  }

  @Test
  void postActivityReturnsErrorIfPostTransferAndPayNotFound() {
    when(activityClient.postTransferAndPayActivity(any(ClientActivityRequest.class)))
        .thenReturn(new ClientActivityResponse("ERROR", "BADBOY", "NO"));
    BffActivityRequest addActivity = new BffActivityRequest();
    addActivity.setToAccountId("toAccount-id");
    addActivity.setFromAccountId("fromAccount-id");
    var response = activityServiceV1.addActivity(addActivity);
    assertEquals("ERROR", response.getStatus());
  }

  @Test
  void verifyAddActivity() {
    ClientActivityResponse clientActivityResponse = new ClientActivityResponse();
    when(activityClient.postTransferAndPayActivity(any(ClientActivityRequest.class)))
        .thenReturn(clientActivityResponse);
    BffActivityRequest addActivity = new BffActivityRequest();
    var responseClient = activityServiceV1.addActivity(addActivity);
    assertNotNull(responseClient);
    assertNotNull(addActivity.getRequestGuid());
  }

  @Test
  void verifyGetTransferLimits() {
    when(activityClient.getTransferLimits(
            "5740834B-C621-D166-5E4A-96FC49CFBF6B", "23E13F71-9BD0-BD5B-3AC9-4902688DAF6F"))
        .thenReturn(mockTransferLimitsResponseClient());
    var responseClient =
        activityServiceV1.getTransferLimits(
            "5740834B-C621-D166-5E4A-96FC49CFBF6B", "23E13F71-9BD0-BD5B-3AC9-4902688DAF6F");
    assertNotNull(responseClient);
  }

  @Test
  void editActivityReturnsBodyIfSuccessful() {
    ClientActivityResponse clientActivityResponse =
        new ClientActivityResponse("SUCCESS", "OK", "Good Job!");

    when(activityClient.editTransferAndPayActivity(any(ClientActivityRequest.class)))
        .thenReturn(clientActivityResponse);

    var bffActivityResponse = activityServiceV1.editActivity(new BffActivityRequest());
    assertEquals("SUCCESS", bffActivityResponse.getStatus());
  }

  @Test
  void handleCompletionClientExceptions() {
    assertThrows(
        CesFeignException.class,
        () ->
            activityServiceV1.handleCompletionExceptions(
                new RuntimeException(
                    new CesFeignException(HttpStatus.BAD_REQUEST, "Just for coverage."))));
  }

  @Test
  void handleCompletionOtherExceptions() {
    final BffException expected = BffException.serviceUnavailable("blah");
    final BffException actual =
        assertThrows(
            BffException.class,
            () ->
                activityServiceV1.handleCompletionExceptions(
                    new RuntimeException("Just for coverage.")));
  }

  @Test
  void getActivity_recentListContainsCorrectStatuses() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    Set<String> expected =
        new HashSet<>(
            Arrays.asList(
                WordUtils.capitalizeFully(STATUS_PROCESSED),
                WordUtils.capitalizeFully(STATUS_CANCELLED),
                WordUtils.capitalizeFully(STATUS_UNSUCCESSFUL),
                WordUtils.capitalizeFully(STATUS_COMPLETE),
                "Decapitalized"));

    var response = activityServiceV1.getActivity(50, 50);
    assertNotNull(response);
    assertTrue(
        response.getRecentActivities().stream()
            .allMatch(e -> expected.contains(e.getDisplayStatus())));
  }

  @Test
  void getActivityReturnsLimitedActivityIfBothLimitsSet() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(5, 5);
    assertEquals(5, result.getRecentActivities().size());
    assertEquals(5, result.getUpcomingActivities().size());

    assertTrue(result.getRecentTruncated());
    assertTrue(result.getUpcomingTruncated());
  }

  @Test
  void getActivityCapitalizesDisplayStatus() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    assertEquals(
        "Scheduled",
        result
            .getRecurringActivities()
            .get(result.getRecurringActivities().size() - 1)
            .getDisplayStatus());
  }

  @Test
  void getActivityDoesNotReturnNullDueDates() {
    when(activityServiceAsync.getActivityAsync())
        .thenReturn(mockClientActivityAsyncResponseWithNullDueDate());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    assertTrue(
        result.getRecentActivities().stream()
            .filter(activity -> activity.getDueDate() == null)
            .toList()
            .isEmpty());
    assertTrue(
        result.getRecurringActivities().stream()
            .filter(activity -> activity.getDueDate() == null)
            .toList()
            .isEmpty());
  }

  @Test
  void getActivityTitleCasesAccountNames() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    assertEquals(
        "5/3 Essential Checking",
        result
            .getRecurringActivities()
            .get(result.getRecurringActivities().size() - 1)
            .getFromAccountName());
    assertEquals(
        "Mastercard Account",
        result
            .getRecurringActivities()
            .get(result.getRecurringActivities().size() - 1)
            .getToAccountName());
  }

  @Test
  void getActivityReturnsAllActivityIfNoLimitSet() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    assertEquals(17, result.getRecentActivities().size());
    assertEquals(66, result.getUpcomingActivities().size());
    assertEquals(6, result.getRecurringActivities().size());
  }

  @Test
  void getActivityReturnsRecurringActivity_WithIsSeriesTemplateSetToTrue() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    assertEquals(17, result.getRecentActivities().size());
    assertEquals(66, result.getUpcomingActivities().size());
    assertEquals(6, result.getRecurringActivities().size());
    result.getRecurringActivities().forEach(a -> assertTrue(a.isSeriesTemplate()));
    result.getRecentActivities().forEach(a -> assertFalse(a.isSeriesTemplate()));
    result.getUpcomingActivities().forEach(a -> assertFalse(a.isSeriesTemplate()));
  }

  @Test
  void getActivityAccountTypeRegression() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockAccountTypeActivityResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    List<BffActivity> recentActivities = result.getRecentActivities();
    List<BffActivity> recurringActivities = result.getRecurringActivities();
    assertTrue(
        recentActivities.stream()
            .anyMatch(a -> a.getType().equals(ActivityBase.INTERNAL_TRANSFER)));
    assertTrue(
        recurringActivities.stream()
            .anyMatch(a -> a.getType().equals(ActivityBase.EXTERNAL_TRANSFER)));
    assertTrue(recentActivities.stream().anyMatch(a -> a.getType().equals(ActivityBase.BILLPAY)));
    // Add additional types as needed.
  }

  @Test
  void getActivityReturnsEmptyActivitiesIfResponseIsNull() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockEmptyActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    assertEquals(0, result.getRecentActivities().size());
    assertEquals(0, result.getUpcomingActivities().size());
  }

  @Test
  void getActivityReturnsLimitedActivityIfOnlyRecentFilter() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(5, null);

    assertEquals(5, result.getRecentActivities().size());
    assertEquals(66, result.getUpcomingActivities().size());
    assertTrue(result.getRecentTruncated());
    assertFalse(result.getUpcomingTruncated());
  }

  @Test
  void getActivityReturnsLimitedActivityIfUpcomingLimitSet() {
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());
    BffGetActivityResponse result = activityServiceV1.getActivity(null, 5);

    assertEquals(17, result.getRecentActivities().size());
    assertEquals(5, result.getUpcomingActivities().size());
    assertFalse(result.getRecentTruncated());
    assertTrue(result.getUpcomingTruncated());
  }

  @Test
  void normalizeDisplayStatusHandlesUnderscores() {
    BffActivity activity = new BffActivity();
    activity.setDisplayStatus("IN_PROCESS");

    assertEquals("In Process", activity.getNormalizedDisplayStatus());
  }

  @Test
  void canceledAndCompleteActivitiesAreMovedToHistory() {
    List<BffActivity> history = new ArrayList<>();
    List<BffActivity> recurring = new ArrayList<>();
    history.add(makeActivityWithStatus(ActivityBase.IN_PROCESS_STATUS));
    history.add(makeActivityWithStatus(ActivityBase.UNSUCCESSFUL_STATUS));
    recurring.add(makeActivityWithStatus(ActivityBase.SCHEDULED_STATUS));
    recurring.add(makeActivityWithStatus(ActivityBase.CANCELED_STATUS));
    recurring.add(makeActivityWithStatus(ActivityBase.COMPLETED_STATUS));
    ActivityUtilities.moveCompleteActivitiesToHistory(history, recurring);
    assertEquals(4, history.size());
    assertEquals(1, recurring.size());
  }

  @Test
  void processedActivitiesAreConvertedToCompleted() {
    var unknownClientTypeResponse = new ClientActivityResponse();
    var clientActivity = new ClientActivity();
    clientActivity.setAmount(1.00);
    clientActivity.setFromAccountId("1");
    clientActivity.setToAccountId("2");
    clientActivity.setType("INTERNAL_TRANSFER");
    clientActivity.setDisplayStatus("Processed");
    clientActivity.setDueDate(LocalDate.now());
    unknownClientTypeResponse.getActivities().add(clientActivity);
    when(activityServiceAsync.getActivityAsync())
        .thenReturn(futureFromClientActivityResponse(unknownClientTypeResponse));
    BffGetActivityResponse result = activityServiceV1.getActivity(null, 5);

    assertEquals("Completed", result.getRecentActivities().get(0).getDisplayStatus());
  }

  @Test
  @DisplayName(
      """
          Get activity with a recurring external transfer series converts the
          numberOfActivities field with a value of -1 to null
                  """)
  void getActivityWithRecurringExternalTransferSeries_convertsNegativeOneToNull() {
    var cesActivityResponse = new ClientActivityResponse();

    var recurringExternalActivity = new ClientActivity();
    recurringExternalActivity.setRecurringId("1234");
    recurringExternalActivity.setNumberOfRemainingActivities(-1);
    recurringExternalActivity.setAmount(50.50);
    recurringExternalActivity.setDueDate(LocalDate.now());
    recurringExternalActivity.setDisplayStatus("Scheduled");

    cesActivityResponse.setRecurringActivities(List.of(recurringExternalActivity));

    when(activityServiceAsync.getActivityAsync())
        .thenReturn(CompletableFuture.supplyAsync(() -> cesActivityResponse));

    var result = activityServiceV1.getActivity(50, 50);

    assertNull(result.getRecurringActivities().get(0).getNumberOfActivities());
  }

  private BffActivity makeActivityWithStatus(String displayStatus) {
    var activity = new BffActivity();
    activity.setDisplayStatus(displayStatus);
    activity.setId(UUID.randomUUID().toString());
    activity.setFromAccountId(UUID.randomUUID().toString());
    activity.setToAccountId(UUID.randomUUID().toString());
    activity.setAmount(0.00);
    activity.setType(ActivityBase.INTERNAL_TRANSFER);
    return activity;
  }

  private <T extends BffResponse> void assertFeignErrorHandled(ResponseEntity<T> result) {
    assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
    assertEquals(BffResponse.BFF_ERROR, result.getBody().getStatus());
  }

  private CompletableFuture<ClientActivityResponse> mockAccountTypeActivityResponse() {
    try {
      return CompletableFuture.supplyAsync(
          () -> mockFromFile("activity_type_regress_activity.json", ClientActivityResponse.class));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private CompletableFuture<ResponseEntity<ProfileResponse>> mockAccountTypeProfileResponse() {
    try {
      return CompletableFuture.supplyAsync(
          () ->
              new ResponseEntity<ProfileResponse>(
                  mockFromFile("activity_type_regress_profile.json", ProfileResponse.class),
                  HttpStatus.OK));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  void getCESActivitiesOnly_ShouldReturnCESActivitiesWithoutCoreTransfers() {
    // Given
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When
    BffGetActivityResponse result = activityServiceV1.getActivity(5, 10);

    // Then
    assertNotNull(result);
    verify(activityServiceAsync, times(1)).getActivityAsync();
    // Verify that only CES activities are returned
    assertFalse(result.getRecentActivities().isEmpty() || result.getUpcomingActivities().isEmpty());
  }

  @Test
  void getActivity_WhenCoreTransfersDisabled_ShouldApplyLimiting() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(false);
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When
    BffGetActivityResponse result = activityServiceV1.getActivity(5, 10);

    // Then
    assertNotNull(result);
    // Verify limiting was applied (should have exactly 5 recent and 10 upcoming)
    assertEquals(5, result.getRecentActivities().size());
    assertEquals(10, result.getUpcomingActivities().size());
    assertTrue(result.getRecentTruncated());
    assertTrue(result.getUpcomingTruncated());
    verify(featureFlagService, times(1)).isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);
  }

  @Test
  void getActivity_WhenCoreTransfersEnabled_ShouldNotApplyLimiting() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When
    BffGetActivityResponse result = activityServiceV1.getActivity(5, 10);

    // Then
    assertNotNull(result);
    // Verify limiting was NOT applied (should return all activities, not limited to 5/10)
    assertEquals(17, result.getRecentActivities().size()); // All recent activities
    assertEquals(66, result.getUpcomingActivities().size()); // All upcoming activities
    assertFalse(result.getRecentTruncated());
    assertFalse(result.getUpcomingTruncated());
    verify(featureFlagService, times(1)).isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);
  }

  @Test
  void getActivity_WhenCoreTransfersDisabledWithNullLimits_ShouldNotApplyLimiting() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(false);
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    // Then
    assertNotNull(result);
    // Even when Core Transfers is disabled, null limits should return all activities
    assertEquals(17, result.getRecentActivities().size());
    assertEquals(66, result.getUpcomingActivities().size());
    assertFalse(result.getRecentTruncated());
    assertFalse(result.getUpcomingTruncated());
    verify(featureFlagService, times(1)).isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);
  }

  @Test
  void getActivity_WhenCoreTransfersEnabledWithNullLimits_ShouldNotApplyLimiting() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When
    BffGetActivityResponse result = activityServiceV1.getActivity(null, null);

    // Then
    assertNotNull(result);
    // When Core Transfers is enabled, limiting should be skipped regardless of limits
    assertEquals(17, result.getRecentActivities().size());
    assertEquals(66, result.getUpcomingActivities().size());
    assertFalse(result.getRecentTruncated());
    assertFalse(result.getUpcomingTruncated());
    verify(featureFlagService, times(1)).isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);
  }

  @Test
  void getActivity_WhenFeatureFlagServiceThrowsException_ShouldDefaultToApplyingLimiting() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED))
        .thenThrow(new RuntimeException("Feature flag service error"));
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When & Then
    // The method should still work and default to applying limiting (safe fallback)
    assertThrows(RuntimeException.class, () -> activityServiceV1.getActivity(5, 10));
    verify(featureFlagService, times(1)).isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);
  }

  @Test
  void getActivity_WhenCoreTransfersDisabledWithZeroLimits_ShouldReturnEmptyLists() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(false);
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When
    BffGetActivityResponse result = activityServiceV1.getActivity(0, 0);

    // Then
    assertNotNull(result);
    // Zero limits should return empty lists when limiting is applied
    assertEquals(0, result.getRecentActivities().size());
    assertEquals(0, result.getUpcomingActivities().size());
    assertTrue(result.getRecentTruncated());
    assertTrue(result.getUpcomingTruncated());
    verify(featureFlagService, times(1)).isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);
  }

  @Test
  void getActivity_WhenCoreTransfersEnabledWithZeroLimits_ShouldReturnAllActivities() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(activityServiceAsync.getActivityAsync()).thenReturn(mockClientActivityAsyncResponse());

    // When
    BffGetActivityResponse result = activityServiceV1.getActivity(0, 0);

    // Then
    assertNotNull(result);
    // When Core Transfers is enabled, limiting should be skipped even with zero limits
    assertEquals(17, result.getRecentActivities().size());
    assertEquals(66, result.getUpcomingActivities().size());
    assertFalse(result.getRecentTruncated());
    assertFalse(result.getUpcomingTruncated());
    verify(featureFlagService, times(1)).isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);
  }
}
