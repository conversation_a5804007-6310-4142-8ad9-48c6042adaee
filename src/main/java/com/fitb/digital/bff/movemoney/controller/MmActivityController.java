/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.*;

import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.featureflags.ActivityFeatureFlagChecker;
import com.fitb.digital.bff.movemoney.model.ActivityType;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.RecurringActivityFrequency;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffAddActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetTransferLimitsResponse;
import com.fitb.digital.bff.movemoney.service.activity.ActivityCombinerService;
import com.fitb.digital.bff.movemoney.service.activity.ActivityService;
import com.fitb.digital.bff.movemoney.service.activity.ActivityServiceV1;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersService;
import com.fitb.digital.bff.movemoney.util.TDSCoreTransferAccountTypeCheck;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import com.fitb.digital.lib.risk.RiskScore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(
    origins = {
      "http://localhost:8080",
      "https://slapdpd002.info53.com",
      "https://developer-stg.info53.com",
      "https://developer.info53.com"
    })
// TODO: @Api(tags = "Transfer and Bill Pay Activity Add/Update/Find/Cancel.")
public class MmActivityController {
  public static final String SUCCESS = "SUCCESS";

  private final ActivityService activityService;
  private final ActivityServiceV1 activityServiceV1;
  private final ActivityCombinerService activityCombinerService;
  private final FeatureFlagService featureFlagService;
  private final ActivityFeatureFlagChecker activityFeatureFlagChecker;
  private final CoreTransfersService coreTransfersService;

  @RiskScore(activityType = "*")
  @PostMapping(value = "/activity", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Create a new transfer or bill pay activity.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public ResponseEntity<BffAddActivityResponse> addActivity(
      @Valid @RequestBody final BffActivityRequest bffActivityRequest) {
    // log.debug("Activity Add Start", kv("request_body", bffActivityRequest));
    sanitizeDueDate(bffActivityRequest);

    activityFeatureFlagChecker.checkFeatureEnabled(bffActivityRequest.getActivityType());

    final boolean OrcFlag = featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED);

    BffAddActivityResponse response = null;

    boolean coreTransfersEnabled = featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);

    if (OrcFlag) {
      response = activityService.addActivity(bffActivityRequest);
    } else if (coreTransfersEnabled && isCoreTransferEligible(bffActivityRequest)) {
      log.info("Making request to Core Transfer TDS");
      response = coreTransfersService.addActivityTds(bffActivityRequest);
    } else {
      log.debug("Making request to CES");
      response = activityServiceV1.addActivity(bffActivityRequest);
    }
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @RiskScore(activityType = "*")
  @PutMapping(value = "/activity", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Update an existing transfer or bill pay activity.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public ResponseEntity<BffAddActivityResponse> editActivity(
      @Valid @RequestBody final BffActivityRequest bffActivityRequest) {
    // log.debug("Activity Edit Start", kv("request_body", bffActivityRequest));
    sanitizeDueDate(bffActivityRequest);

    activityFeatureFlagChecker.checkFeatureEnabled(bffActivityRequest.getActivityType());

    final BffAddActivityResponse response =
        featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)
            ? activityService.editActivity(bffActivityRequest)
            : activityServiceV1.editActivity(bffActivityRequest);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @GetMapping(value = "/v2/activity", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Return transfer and bill pay activity for the current user.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public BffGetActivityResponse getActivityV2(
      @RequestParam(value = "recentLimit", required = false) Integer recentLimit,
      @RequestParam(value = "upcomingLimit", required = false) Integer upcomingLimit) {

    if (featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)) {
      return activityService.getActivity(recentLimit, upcomingLimit);
    } else if (featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)) {
      // New parallel processing with CES and Core Transfers
      return activityCombinerService.getCombinedActivities(recentLimit, upcomingLimit);
    } else {
      return activityServiceV1.getActivity(recentLimit, upcomingLimit);
    }
  }

  @GetMapping(value = "/activity/transferlimits", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Return transfer limits for the pair of accounts.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public BffGetTransferLimitsResponse getTransferLimits(
      @RequestParam(value = "fromAccountId", required = false) String fromAccountId,
      @RequestParam(value = "toAccountId", required = false) String toAccountId) {
    return activityServiceV1.getTransferLimits(fromAccountId, toAccountId);
  }

  @DeleteMapping(value = "/activity")
  @Operation(summary = "Cancel a transfer or bill pay activity.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public BffResponse cancelActivity(
      @RequestParam String activityId, @RequestParam(required = false) String activityType) {
    if (featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)) {
      return activityService.cancelActivity(activityId, activityType);
    } else {
      return activityServiceV1.cancelActivity(activityId);
    }
  }

  @Nullable
  private void sanitizeDueDate(final BffActivityRequest bffActivityRequest) {
    final var easternDateTime = getCurrentEasternZonedDateTime();
    if (bffActivityRequest.isScheduleImmediately()) {
      final var currentEasternDate = easternDateTime.toLocalDate();
      final var daysBetween =
          ChronoUnit.DAYS.between(currentEasternDate, bffActivityRequest.getDueDate());

      if (Math.abs(daysBetween) > 1) {
        throw BffException.badRequest(ExceptionStatus.INVALID_DUE_DATE);
      }

      bffActivityRequest.setDueDate(currentEasternDate);

    } else if (bffActivityRequest.getDueDate() == null) {
      bffActivityRequest.setDueDate(easternDateTime.toLocalDate());
    }
  }

  private boolean isCoreTransferEligible(BffActivityRequest bffActivityRequest) {
    if (bffActivityRequest.getActivityType().equals(ActivityType.INTERNAL_TRANSFER)
        && TDSCoreTransferAccountTypeCheck.isAccountTypeAllowed(
            bffActivityRequest.getFromAccountType(), bffActivityRequest.getToAccountType())) {
      return bffActivityRequest.getFrequency().equals(RecurringActivityFrequency.ONE_TIME)
          && bffActivityRequest.getDueDate().isEqual(LocalDate.now());
    }
    return false;
  }

  @NotNull
  protected ZonedDateTime getCurrentEasternZonedDateTime() {
    final var utcCurrentDateTime = Instant.now();
    return ZonedDateTime.ofInstant(utcCurrentDateTime, ZoneId.of("America/New_York"));
  }
}
