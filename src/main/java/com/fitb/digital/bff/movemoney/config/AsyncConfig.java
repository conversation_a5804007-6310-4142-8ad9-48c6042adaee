/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import java.util.concurrent.Executor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.config.MethodInvokingFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskDecorator;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * Configures a Thread Pool, and most importantly a DelegatingSecurityContextAsyncTaskExecutor to
 * make sure that the security context of the current tread is propogated to any further threads.
 *
 * <p>If using CompletableFuture.supplyAsync, make sure to supply the TaskExecutor as the second
 * param to use this pool otherwise the use of Spring's @Async will automatically use this
 * configured pool.
 *
 * <pre>
 * @Async
 * CompletableFuture<Customer>> getCustomerFuture() {
 *   return CompletableFuture.completedFuture(customerService.getCustomer());
 * }
 * </pre>
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig implements AsyncConfigurer {

  @Override
  public Executor getAsyncExecutor() {
    return delegatingSecurityContextAsyncTaskExecutor();
  }

  /**
   * Set up a Task executor that will handle delegating our security context. Not stricly necessary
   * to create a bean, as we could supply it in the above getAsyncExecutor, however this provides
   * some hope that if someone @Autowire in a TaskExecutor that they'll get this bean.
   *
   * @return
   */
  @Bean()
  @Primary
  public TaskExecutor delegatingSecurityContextAsyncTaskExecutor() {
    final ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(10);
    executor.setMaxPoolSize(100);
    executor.setQueueCapacity(50);
    executor.setWaitForTasksToCompleteOnShutdown(true);
    executor.setTaskDecorator(new ContextCopyingDecorator());
    executor.setThreadNamePrefix("DelegatingSecurityContextAsyncTaskExecutor-");
    executor.initialize();

    return new DelegatingSecurityContextAsyncTaskExecutor(executor) {
      public void shutdown() {
        executor.destroy();
      }
    };
  }

  /**
   * if the return type is void, exceptions will not be propagated to the calling thread. Hence we
   * need to add extra configurations to handle exceptions.
   *
   * @return
   */
  @Override
  public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
    return (ex, method, params) ->
        log.error("Uncaught exception from void method: " + method.getName(), ex);
  }

  @Bean
  public MethodInvokingFactoryBean methodInvokingFactoryBean() {
    MethodInvokingFactoryBean methodInvokingFactoryBean = new MethodInvokingFactoryBean();
    methodInvokingFactoryBean.setTargetClass(SecurityContextHolder.class);
    methodInvokingFactoryBean.setTargetMethod("setStrategyName");
    methodInvokingFactoryBean.setArguments(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
    return methodInvokingFactoryBean;
  }

  static class ContextCopyingDecorator implements TaskDecorator {
    @NotNull
    @Override
    public Runnable decorate(@NotNull Runnable runnable) {
      RequestAttributes context = null;
      try {
        context = RequestContextHolder.currentRequestAttributes();
      } catch (IllegalStateException e) {
        log.debug("No request context available for thread: {}", Thread.currentThread().getName());
        // No request context available, proceed without it
      }

      final RequestAttributes finalContext = context;
      return () -> {
        try {
          if (finalContext != null) {
            RequestContextHolder.setRequestAttributes(finalContext);
          }
          runnable.run();
        } finally {
          if (finalContext != null) {
            RequestContextHolder.resetRequestAttributes();
          }
        }
      };
    }
  }
}
